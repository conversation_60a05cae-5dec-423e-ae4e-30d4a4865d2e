using System;
using System.IO;
using MySqlConnector;

class DatabaseSetup
{
    static void Main(string[] args)
    {
        string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;";
        string databaseName = "myadaptivecloud";

        try
        {
            // First, create the database if it doesn't exist
            using (var connection = new MySqlConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine("Connected to MySQL server successfully.");

                var createDbCommand = new MySqlCommand($"CREATE DATABASE IF NOT EXISTS {databaseName};", connection);
                createDbCommand.ExecuteNonQuery();
                Console.WriteLine($"Database '{databaseName}' created or already exists.");
            }

            // Now connect to the specific database and create tables
            connectionString += $"Database={databaseName};";
            using (var connection = new MySqlConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine($"Connected to database '{databaseName}' successfully.");

                // Execute Data Protection configuration fix
                string fixSqlPath = @"fix_data_protection_config.sql";
                if (File.Exists(fixSqlPath))
                {
                    string fixSql = File.ReadAllText(fixSqlPath);

                    // Split by semicolon and execute each statement
                    var statements = fixSql.Split(';', StringSplitOptions.RemoveEmptyEntries);

                    foreach (var statement in statements)
                    {
                        var sql = statement.Trim();
                        if (string.IsNullOrWhiteSpace(sql)) continue;
                        if (sql.StartsWith("--")) continue; // Skip comments

                        try
                        {
                            var command = new MySqlCommand(sql, connection);
                            var result = command.ExecuteScalar();
                            Console.WriteLine($"Executed: {sql.Substring(0, Math.Min(80, sql.Length))}...");
                            if (result != null)
                            {
                                Console.WriteLine($"Result: {result}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error executing SQL: {sql.Substring(0, Math.Min(50, sql.Length))}...");
                            Console.WriteLine($"Error: {ex.Message}");
                        }
                    }

                    Console.WriteLine("Data Protection configuration fix completed.");
                }
                else
                {
                    Console.WriteLine($"Fix SQL file not found: {fixSqlPath}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }

        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
