using Microsoft.Extensions.Caching.Memory;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Data.Repositories;
using MyAdaptiveCloud.Services.DTOs.Configuration;
using MyAdaptiveCloud.Services.DTOs.DDoSMitigation;
using MyAdaptiveCloud.Services.DTOs.Msrc;
using MyAdaptiveCloud.Services.Requests.Configuration;

namespace MyAdaptiveCloud.Services.Services
{
    public class ConfigurationService(
        IMemoryCache cache,
        IConfigurationRepository configurationRepository,
        IOrganizationRepository organizationRepository) : IConfigurationService
    {
        private readonly IConfigurationRepository _configurationRepository = configurationRepository;
        private readonly IOrganizationRepository _organizationRepository = organizationRepository;
        private readonly IMemoryCache _cache = cache;
        private readonly Dictionary<string, List<Action>> _evictionHandlers = [];

        public async Task<List<ConfigurationDTO>> GetList(int organizationId)
        {
            var result = await _configurationRepository.GetByOrganization(organizationId);
            var resultByCategory = result.GroupBy(x => x.Category).OrderBy(x => x.Key);

            return resultByCategory.Select(x => new ConfigurationDTO
            {
                Id = x.First().Id,
                Category = x.Key,
                ConfigurationValues = x.OrderBy(value => value.Name).Select(value => new ConfigurationValueDTO
                {
                    Id = value.ConfigurationId,
                    Name = value.Name,
                    Value = !value.IsSecret ? value.Value : Constants.SecretValueMessage,
                    InputType = value.InputType,
                    IsSecret = value.IsSecret
                }).ToList()
            }).ToList();
        }

        /// <summary>
        ///     Updates the configuration values supplied in the request.
        ///     Only ROOT organization configuration values can be updated using this method.
        /// </summary>
        public async Task UpdateConfigurationValues(int userId, int organizationId, EditConfigurationRequest request)
        {
            var recordsToUpdate = request.Values
                .Where(x => x.Value.Trim() != Constants.SecretValueMessage)
                .Select(x => x.Id.Value);
            var configurationValues = await _configurationRepository.GetValuesByOrganizationAndIds(organizationId, recordsToUpdate);
            foreach (var configurationValue in configurationValues)
            {
                configurationValue.Value = request.Values.First(x => x.Id == configurationValue.ConfigurationValuesId).Value;
                configurationValue.UpdatedBy = userId;
                configurationValue.UpdatedOn = DateTime.UtcNow;
            }

            await _configurationRepository.SaveChanges();
            ExpireCache(organizationId);
        }

        public async Task<CloudInfrastructureConfigurationDTO> GetCloudInfrastructureConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(CloudInfrastructureConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(CloudInfrastructureConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new CloudInfrastructureConfigurationDTO
                {
                    CName = configurationDtos.First(v => v.Name == "CNAME").Value,
                    UserRole = configurationDtos.FirstOrDefault(v => v.Name == "UserRole")?.Value,
                    AllowedDomains = (configurationDtos.FirstOrDefault(v => v.Name == "Allowed Domains")?.Value ?? "")
                        .Split(',', StringSplitOptions.RemoveEmptyEntries).Select(x => x.Trim())
                        .ToList(),
                    ApiUrl = configurationDtos.FirstOrDefault(c => c.Name == "ApiUrl")?.Value ?? "",
                    ApiVersion = configurationDtos.FirstOrDefault(c => c.Name == "Api Version")?.Value ?? ""
                };
            });
        }

        public async Task<EmailSenderWorkerConfigurationDTO> GetEmailSenderWorkerConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(EmailSenderWorkerConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(EmailSenderWorkerConfigurationDTO.Category);
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();

                return new EmailSenderWorkerConfigurationDTO
                {
                    Email = configurationDtos.First(v => v.Name == "smtpEmail").Value,
                    UserName = configurationDtos.First(v => v.Name == "smtpUserName").Value,
                    Password = configurationDtos.First(v => v.Name == "smtpPassword").Value,
                    Port = int.Parse(configurationDtos.First(v => v.Name == "smtpPort").Value),
                    WorkerDelay = int.Parse(configurationDtos.First(v => v.Name == "smtpWorkerDelay").Value),
                    Server = configurationDtos.First(v => v.Name == "smtpServer").Value,
                    SendApproval = bool.Parse(configurationDtos.First(v => v.Name == "smtpSendApproval").Value),
                    SenderEmail = configurationDtos.First(v => v.Name == "smtpSenderEmail").Value,
                    RegistrationEmail = (configurationDtos.FirstOrDefault(v => v.Name == "Registration Email")?.Value ?? "")
                        .Split(',', StringSplitOptions.RemoveEmptyEntries).Select(x => x.Trim())
                        .ToList(),
                };
            });
        }

        public async Task<ImapConfigurationDTO> GetImapConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(ImapConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(ImapConfigurationDTO.Category);
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();

                return new ImapConfigurationDTO
                {
                    Email = configurationDtos.First(v => v.Name == "imapEmail").Value,
                    Password = configurationDtos.First(v => v.Name == "imapPassword").Value,
                    Port = int.Parse(configurationDtos.First(v => v.Name == "imapPort").Value),
                    Server = configurationDtos.First(v => v.Name == "imapServer").Value,
                    WorkerDelay = int.Parse(configurationDtos.First(v => v.Name == "imapWorkerDelay").Value)
                };
            });
        }

        public async Task<ConnectWiseConfigurationDTO> GetConnectWiseConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(ConnectWiseConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(ConnectWiseConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new ConnectWiseConfigurationDTO
                {
                    BaseUrl = configurationDtos.FirstOrDefault(v => v.Name == "CWBaseUrl")?.Value,
                    Company = configurationDtos.FirstOrDefault(v => v.Name == "CWCompany")?.Value,
                    PublicKey = configurationDtos.FirstOrDefault(v => v.Name == "CWPublicKey")?.Value,
                    PrivateKey = configurationDtos.FirstOrDefault(v => v.Name == "CWPrivateKey")?.Value,
                    ClientId = configurationDtos.FirstOrDefault(v => v.Name == "CWClientId")?.Value,
                    AgreementName =
                        configurationDtos.FirstOrDefault(v => v.Name == "Cloud Infrastructure Agreement Name")?.Value ??
                        "Cloud Infrastructure",
                    AgreementTypeName =
                        configurationDtos.FirstOrDefault(v => v.Name == "Cloud Infrastructure Agreement Type Name")?.Value ??
                        "AdaptiveCloud",
                    BillingCycleName =
                        configurationDtos.FirstOrDefault(v => v.Name == "Cloud Infrastructure Billing Cycle Type Name")?.Value ??
                        "Monthly",
                    DuplicateFieldName = configurationDtos.FirstOrDefault(v => v.Name == "Company Duplicate Field Name")?.Value ??
                                         "Duplicate",
                    PartnerTypeName = configurationDtos.FirstOrDefault(v => v.Name == "Partner Type")?.Value ?? "Partner",
                    CustomerTypeName = configurationDtos.FirstOrDefault(v => v.Name == "Customer Type")?.Value ?? "Customer",
                    CloudInfrastructureValidationProducts =
                        (configurationDtos.FirstOrDefault(v => v.Name == "Cloud Infrastructure Validation Products")?.Value ??
                         "ACLOUD-VCPU")
                        .Split(',', StringSplitOptions.RemoveEmptyEntries).Select(x => x.Trim())
                        .ToList(),
                    CommonEmailDomains = (configurationDtos.FirstOrDefault(v => v.Name == "Common Email Domains")?.Value ?? "")
                        .Split(',', StringSplitOptions.RemoveEmptyEntries).Select(x => x.Trim())
                        .ToList(),
                    InvoiceQueryMonths =
                        int.Parse(configurationDtos.FirstOrDefault(v => v.Name == "Invoice Query Months")?.Value ?? "12"),
                    ExcludeMyAdaptiveCloudType = configurationDtos.FirstOrDefault(v => v.Name == "Exclude From MyAdaptiveCloud Type")?.Value,
                };
            });
        }

        public Task<PowerDNSConfigurationDTO> GetPowerDNSConfig()
        {
            return _cache.GetOrCreate(GetCacheKeyByCategoryAndOrganizationId(PowerDNSConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(PowerDNSConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new PowerDNSConfigurationDTO()
                {
                    PDNSApiKey = configurationDtos.FirstOrDefault(v => v.Name == "PDNSApiKey")?.Value,
                    PDNSBaseUrl = configurationDtos.FirstOrDefault(v => v.Name == "PDNSBaseUrl")?.Value,
                    PDNSDefaultTTL = configurationDtos.FirstOrDefault(v => v.Name == "PDNSDefaultTTL")?.Value
                };
            });
        }

        public async Task<AgentAuthenticationConfigurationDTO> GetAgentAuthenticationConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(AgentAuthenticationConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(AgentAuthenticationConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new AgentAuthenticationConfigurationDTO
                {
                    TimeStampHeaderName = configurationDtos.FirstOrDefault(w => w.Name == "TimeStampHeaderName")?.Value ?? "",
                    ApiKeyHeaderName = configurationDtos.FirstOrDefault(w => w.Name == "ApiKeyHeaderName")?.Value ?? "",
                    AuthenticationMethodName = configurationDtos.FirstOrDefault(w => w.Name == "AuthenticationMethodName")?.Value ?? "",
                    RequestMaxAgeInSeconds = int.Parse(configurationDtos.FirstOrDefault(w => w.Name == "RequestMaxAgeInSeconds")?.Value ?? "0")
                };
            });
        }

        public async Task<AgentRemoteConfigurationDTO> GetAgentRemoteConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(AgentRemoteConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(AgentRemoteConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new AgentRemoteConfigurationDTO
                {
                    BaseUrl = configurationDtos.FirstOrDefault(v => v.Name == "AgentRemoteBaseUrl")?.Value,
                    ApiKey = configurationDtos.FirstOrDefault(v => v.Name == "AgentRemoteApiKey")?.Value,
                    ApiSecret = configurationDtos.FirstOrDefault(v => v.Name == "AgentRemoteApiSecret")?.Value,
                    RemoteDisplayBaseUrl = configurationDtos.FirstOrDefault(v => v.Name == "AgentRemoteDisplayBaseUrl")?.Value,
                    RemoteDisplayConnectionStatusCheckInterval =
                        int.Parse(configurationDtos.FirstOrDefault(v => v.Name == "AgentRemoteDisplayConnectionStatusCheckInterval")?.Value ?? "17"),
                };
            });
        }

        public async Task<CloudInfrastructureUsageLimitConfigurationDTO> GetCloudInfrastructureUsageLimitConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(CloudInfrastructureUsageLimitConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(CloudInfrastructureUsageLimitConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new CloudInfrastructureUsageLimitConfigurationDTO
                {
                    Instances = int.Parse(configurationDtos.FirstOrDefault(v => v.Name == "Instances")?.Value ?? "2"),
                    PublicIp = int.Parse(configurationDtos.FirstOrDefault(v => v.Name == "PublicIp")?.Value ?? "1"),
                    Cpu = int.Parse(configurationDtos.FirstOrDefault(v => v.Name == "Cpu")?.Value ?? "8"),
                    Memory = int.Parse(configurationDtos.FirstOrDefault(v => v.Name == "Memory")?.Value ?? "16000"),
                    PrimaryStorage =
                        int.Parse(configurationDtos.FirstOrDefault(v => v.Name == "PrimaryStorage")?.Value ?? "500"),
                    SecondaryStorage =
                        int.Parse(configurationDtos.FirstOrDefault(v => v.Name == "SecondaryStorage")?.Value ?? "500"),
                };
            });
        }

        public async Task<CloudInfrastructureReconcileConfigurationDTO> GetCloudInfrastructureReconcileConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(CloudInfrastructureReconcileConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(CloudInfrastructureReconcileConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new CloudInfrastructureReconcileConfigurationDTO
                {
                    Enabled = bool.Parse(configurationDtos.First(v => v.Name == "Enabled").Value),
                    EnableRenaming = bool.Parse(configurationDtos.First(v => v.Name == "EnableRenaming").Value)
                };
            });
        }

        public async Task<OnTapConfigurationDTO> GetOntapConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(OnTapConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(OnTapConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new OnTapConfigurationDTO()
                {
                    BaseUrl = configurationDtos.FirstOrDefault(v => v.Name == "OntapBaseUrl")?.Value,
                    User = configurationDtos.FirstOrDefault(v => v.Name == "OntapUser")?.Value,
                    Password = configurationDtos.FirstOrDefault(v => v.Name == "OntapPassword")?.Value,
                    AggregateName = configurationDtos.FirstOrDefault(v => v.Name == "OntapAggregate")?.Value,
                    BasePortName = configurationDtos.FirstOrDefault(v => v.Name == "OntapPort")?.Value,
                    DisableSslChecks = configurationDtos.FirstOrDefault(v => v.Name == "OntapDisableSslChecks")?.Value
                };
            });
        }

        public async Task<AlertRuleEvaluatorWorkerConfigurationDTO> GetAlertRuleEvaluatorWorkerConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(AlertRuleEvaluatorWorkerConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(AlertRuleEvaluatorWorkerConfigurationDTO.Category);
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();

                return new AlertRuleEvaluatorWorkerConfigurationDTO
                {
                    WorkerDelay = configurationDtos.Count != 0 ? int.Parse(configurationDtos.First(v => v.Name.EndsWith(WorkerConfigurationBase.WorkerDelayKey)).Value) : 0,
                    AlertsToProcessChunkSize = configurationDtos.Count != 0
                        ? int.Parse(configurationDtos.First(v => v.Name == "AlertsToProcessChunkSize").Value)
                        : 50,
                };
            });
        }

        public async Task<MetricsProcessorWorkerConfigurationDTO> GetMetricsProcessorWorkerConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(MetricsProcessorWorkerConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(MetricsProcessorWorkerConfigurationDTO.Category);
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();

                return new MetricsProcessorWorkerConfigurationDTO
                {
                    WorkerDelay = configurationDtos.Count != 0 ? int.Parse(configurationDtos.First(v => v.Name.EndsWith(WorkerConfigurationBase.WorkerDelayKey)).Value) : 0,
                    AgentsToProcessChunkSize = configurationDtos.Count != 0
                        ? int.Parse(configurationDtos.First(v => v.Name == "AgentsToProcessChunkSize").Value)
                        : 50,
                };
            });
        }

        public async Task<AgentCleanupProcessorWorkerConfigurationDTO> GetAgentCleanupProcessorWorkerConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(AgentCleanupProcessorWorkerConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(AgentCleanupProcessorWorkerConfigurationDTO.Category);
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();

                var agentRecordsToProcessChunkSizeValue = configurationDtos.FirstOrDefault(v => v.Name == "AgentRecordsToProcessChunkSize")?.Value;
                return new AgentCleanupProcessorWorkerConfigurationDTO
                {
                    WorkerDelay = configurationDtos.Count != 0 ? int.Parse(configurationDtos.First(v => v.Name.EndsWith(WorkerConfigurationBase.WorkerDelayKey)).Value) : 0,
                    AgentRecordsToProcessChunkSize = !String.IsNullOrEmpty(agentRecordsToProcessChunkSizeValue) ? int.Parse(agentRecordsToProcessChunkSizeValue) : 5000,
                };
            });
        }

        public Task<ReportsProcessorConfigurationDTO> GetReportsProcessorConfiguration()
        {
            return _cache.GetOrCreateAsync(ReportsProcessorConfigurationDTO.Category, async entry =>
            {
                entry.AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow).AddMinutes(5);
                var configurationDtos = await GetByCategory(ReportsProcessorConfigurationDTO.Category);

                return new ReportsProcessorConfigurationDTO
                {
                    WorkerDelay = int.Parse(configurationDtos.FirstOrDefault(v => v.Name.EndsWith(WorkerConfigurationBase.WorkerDelayKey))?.Value ?? "0"),
                    ReportsBackendDirectory = configurationDtos.FirstOrDefault(v => v.Name.Equals("ReportsBackendDirectory", StringComparison.OrdinalIgnoreCase))?.Value ?? "",
                    ReportsProcessorDirectory = configurationDtos.FirstOrDefault(v => v.Name.Equals("ReportsProcessorDirectory", StringComparison.OrdinalIgnoreCase))?.Value ?? "",
                };
            });
        }

        public async Task<DeclineUpdatesProcessorWorkerConfigurationDTO> GetDeclineUpdatesProcessorWorkerConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(DeclineUpdatesProcessorWorkerConfigurationDTO.Category), async entry =>
            {
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();
                var configurationDtos = await GetByCategory(DeclineUpdatesProcessorWorkerConfigurationDTO.Category);

                return new DeclineUpdatesProcessorWorkerConfigurationDTO
                {
                    WorkerDelay = configurationDtos.Count != 0 ? int.Parse(configurationDtos.First(v => v.Name.EndsWith(WorkerConfigurationBase.WorkerDelayKey)).Value) : 0
                };
            });
        }

        public async Task<ApproveUpdatesProcessorWorkerConfigurationDTO> GetApproveUpdatesProcessorWorkerConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(ApproveUpdatesProcessorWorkerConfigurationDTO.Category), async entry =>
            {
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();
                var configurationDtos = await GetByCategory(ApproveUpdatesProcessorWorkerConfigurationDTO.Category);

                return new ApproveUpdatesProcessorWorkerConfigurationDTO
                {
                    WorkerDelay = configurationDtos.Count != 0 ? int.Parse(configurationDtos.First(v => v.Name.EndsWith(WorkerConfigurationBase.WorkerDelayKey)).Value) : 0
                };
            });
        }

        public async Task<AgentUpgradeProcessorWorkerConfigurationDTO> AgentUpgradeProcessorWorkerConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(ApproveUpdatesProcessorWorkerConfigurationDTO.Category), async entry =>
            {
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();
                var configurationDtos = await GetByCategory(AgentUpgradeProcessorWorkerConfigurationDTO.Category);

                return new AgentUpgradeProcessorWorkerConfigurationDTO
                {
                    WorkerDelay = configurationDtos.Count != 0 ? int.Parse(configurationDtos.First(v => v.Name.EndsWith(WorkerConfigurationBase.WorkerDelayKey)).Value) : 0
                };
            });
        }

        public async Task<AutoApproveUpdatesProcessorWorkerConfiguration> GetAutoApproveUpdatesProcessorWorkerConfiguration()
        {
            return await _cache.GetOrCreateAsync(AutoApproveUpdatesProcessorWorkerConfiguration.Category, async entry =>
            {
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();
                var configurationDtos = await GetByCategory(AutoApproveUpdatesProcessorWorkerConfiguration.Category);

                return new AutoApproveUpdatesProcessorWorkerConfiguration
                {
                    WorkerDelay = configurationDtos.Count != 0 ? int.Parse(configurationDtos.First(v => v.Name.EndsWith(WorkerConfigurationBase.WorkerDelayKey)).Value) : 0
                };
            });
        }

        public async Task<List<FeatureFlagConfigurationDTO>> GetFeatureFlagsConfiguration(int organizationId)
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(FeatureFlagConfigurationDTO.Category, organizationId), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                var configurationDtos = await GetByCategory(FeatureFlagConfigurationDTO.Category, organizationId);

                return configurationDtos
                    .Select(ff =>
                        new FeatureFlagConfigurationDTO
                        {
                            FeatureFlagName = ff.Name,
                            FeatureFlagState = ff.Value == "true"
                        }).ToList();
            });
        }

        public async Task<FileAdministrationConfigurationDTO> GetFileAdministrationConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(FileAdministrationConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(FileAdministrationConfigurationDTO.Category);

                var maximumFileSize = configurationDtos.SingleOrDefault(cv => cv.Name == "MaximumFileSize")?.Value;
                bool hasMaximumSize = long.TryParse(maximumFileSize, out long mFileSize);

                return new FileAdministrationConfigurationDTO
                {
                    FolderPath = configurationDtos.SingleOrDefault(cv => cv.Name == "FolderPath")?.Value,
                    MaximumFileSize = hasMaximumSize ? mFileSize * 1024 * 1024 : null,
                };
            });
        }

        public async Task<AdaptiveCloudApiConfigurationDTO> GetAdaptiveCloudApiConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(AdaptiveCloudApiConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(AdaptiveCloudApiConfigurationDTO.Category);

                return new AdaptiveCloudApiConfigurationDTO
                {
                    CSBaseUrl = configurationDtos.SingleOrDefault(cv => cv.Name == "CSBaseUrl")?.Value,
                    CSApiKey = configurationDtos.SingleOrDefault(cv => cv.Name == "CSApiKey")?.Value,
                    CSSecretKey = configurationDtos.SingleOrDefault(cv => cv.Name == "CSSecretKey")?.Value,
                };
            });
        }

        public async Task<TotalTechConfigurationDTO> GetTotalTechConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(TotalTechConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                var configurationDtos = await GetByCategory(TotalTechConfigurationDTO.Category);

                int folderId = 0;
                if (int.TryParse(configurationDtos.SingleOrDefault(cv => cv.Name == "FolderId")?.Value, out var v))
                {
                    folderId = v;
                }

                var serviceIdValue = configurationDtos.SingleOrDefault(cv => cv.Name == "ServiceIds");
                var serviceIds = new List<int>();
                if (!string.IsNullOrEmpty(serviceIdValue?.Value))
                {
                    serviceIds = serviceIdValue.Value.Split(',').Select(serviceId => int.Parse(serviceId)).ToList();
                }

                return new TotalTechConfigurationDTO
                {
                    ServiceIds = serviceIds,
                    FolderId = folderId
                };
            });
        }

        public async Task<NetworkFileServersConfigurationDTO> GetNetworkFileServersConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(NetworkFileServersConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(NetworkFileServersConfigurationDTO.Category);

                return new NetworkFileServersConfigurationDTO
                {
                    MaximumAutoSize = long.Parse(configurationDtos.SingleOrDefault(cv => cv.Name.ToLower() == "maximum auto-size gb")?.Value),
                    MaximumVolumeSize = long.Parse(configurationDtos.SingleOrDefault(cv => cv.Name.ToLower() == "maximum volume size gb")?.Value),
                    FileSizeGrowthThreshold = int.Parse(configurationDtos.SingleOrDefault(cv => cv.Name.ToLower() == "file size growth threshold")?.Value),
                    FileSizeShrinkThreshold = int.Parse(configurationDtos.SingleOrDefault(cv => cv.Name.ToLower() == "file size shrink threshold")?.Value)
                };
            });
        }

        public async Task<TenaxConfigurationDTO> GetTenaxConfiguration()
        {
            return await _cache.GetOrCreate(GetCacheKeyByCategoryAndOrganizationId(TenaxConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(TenaxConfigurationDTO.Category);

                return new TenaxConfigurationDTO
                {
                    BaseURL = configurationDtos.SingleOrDefault(cv => cv.Name.Equals(TenaxConstants.TenaxBaseURL, StringComparison.CurrentCultureIgnoreCase))
                        ?.Value,
                    ApiKey = configurationDtos.SingleOrDefault(cv => cv.Name.Equals(TenaxConstants.TenaxApiKey, StringComparison.CurrentCultureIgnoreCase))
                        ?.Value,
                    SecretKey = configurationDtos
                        .SingleOrDefault(cv => cv.Name.Equals(TenaxConstants.TenaxSecretKey, StringComparison.CurrentCultureIgnoreCase)).Value
                };
            });
        }

        public async Task<PartnerResourceHubConfigurationDTO> GetPartnerResourceHubConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(PartnerResourceHubConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(PartnerResourceHubConfigurationDTO.Category);

                int folderId = 0;
                if (int.TryParse(configurationDtos.SingleOrDefault(cv => cv.Name == "FolderId")?.Value, out var v))
                {
                    folderId = v;
                }

                return new PartnerResourceHubConfigurationDTO
                {
                    FolderId = folderId
                };
            });
        }

        public async Task<HomeConfigurationDTO> GetHomeConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(HomeConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(HomeConfigurationDTO.Category);

                return new HomeConfigurationDTO
                {
                    Heading = configurationDtos?.SingleOrDefault(cv => cv.Name == "Heading")?.Value,
                    SubHeading = configurationDtos?.SingleOrDefault(cv => cv.Name == "Sub Heading")?.Value,
                    Content = configurationDtos?.SingleOrDefault(cv => cv.Name == "Content")?.Value,
                };
            });
        }

        public async Task<SoftwareLicenseAgreementsConfigurationDTO> GetSoftwareLicenseAgreementsConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(SoftwareLicenseAgreementsConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(SoftwareLicenseAgreementsConfigurationDTO.Category);

                return new SoftwareLicenseAgreementsConfigurationDTO
                {
                    Authorization = configurationDtos?.SingleOrDefault(cv => cv.Name == "Software License Agreement Authorization")?.Value,
                    Agreement = configurationDtos?.SingleOrDefault(cv => cv.Name == "Software License Agreement")?.Value,
                    UpdatedDate = configurationDtos?.SingleOrDefault(cv => cv.Name == "Software License Agreement Updated Date")?.Value,
                };
            });
        }

        public async Task<WhiteLabelConfigurationDTO> GetWhiteLabelConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(WhiteLabelConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(WhiteLabelConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new WhiteLabelConfigurationDTO
                {
                    Default = configurationDtos.SingleOrDefault(v => v.Name == "Default").Value,
                };
            });
        }

        public async Task<DataProtectionConfigurationDTO> GetDataProtectionConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(DataProtectionConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(DataProtectionConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new DataProtectionConfigurationDTO
                {
                    BaseURL = configurationDtos.SingleOrDefault(v => v.Name == "BaseURL")?.Value,
                    ClientId = configurationDtos.SingleOrDefault(v => v.Name == "ClientId")?.Value,
                    ClientSecret = configurationDtos.SingleOrDefault(v => v.Name == "ClientSecret")?.Value,
                };
            });
        }

        public async Task<KeyCloakConfigurationDTO> GetKeyCloakConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(KeyCloakConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(KeyCloakConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new KeyCloakConfigurationDTO
                {
                    AuthorityKey = configurationDtos.SingleOrDefault(v => v.Name == "Authority")?.Value ?? "",
                    ApiKey = configurationDtos.SingleOrDefault(v => v.Name == "ClientId")?.Value ?? "",
                    ApiSecretKey = configurationDtos.SingleOrDefault(v => v.Name == "ClientSecret")?.Value ?? "",
                };
            });
        }

        public async Task<KeyCloakServiceConfigurationDTO> GetKeyCloakServiceConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(KeyCloakServiceConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(KeyCloakServiceConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new KeyCloakServiceConfigurationDTO
                {
                    AuthorityKey = configurationDtos.SingleOrDefault(v => v.Name == "Authority")?.Value ?? "",
                    ApiKey = configurationDtos.SingleOrDefault(v => v.Name == "ClientId")?.Value ?? "",
                    ApiSecretKey = configurationDtos.SingleOrDefault(v => v.Name == "ClientSecret")?.Value ?? "",
                };
            });
        }

        public async Task<CookiesConfigurationDTO> GetCookiesConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(CookiesConfigurationDTO.Category), async entry =>
            {
                var configurationDtos = await GetByCategory(CookiesConfigurationDTO.Category);
                entry.SlidingExpiration = TimeSpan.FromDays(1);

                return new CookiesConfigurationDTO
                {
                    ExpiryTimeInMinutes = int.TryParse(configurationDtos.FirstOrDefault(cv => cv.Name == "ExpiryTimeInMinutes")?.Value, out int expiryTimeInMinutes)
                        ? expiryTimeInMinutes
                        : 0
                };
            });
        }

        public async Task<AgentConfigurationDTO> GetAgentConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(AgentConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(AgentConfigurationDTO.Category);

                return new AgentConfigurationDTO
                {
                    MinAgentVersionImmediateUpdateInstall = configurationDtos.SingleOrDefault(cv => cv.Name == "MinAgentVersionImmediateUpdateInstall")?.Value,
                    MinAgentVersionToUseActionsViaWebsocket = configurationDtos.SingleOrDefault(cv => cv.Name == "MinAgentVersionToUseActionsViaWebsocket")?.Value,
                    MaxConcurrentAgentUpgrades =
                        int.TryParse(configurationDtos.SingleOrDefault(cv => cv.Name == "MaxConcurrentAgentUpgrades")?.Value, out var maxConcurrentAgentUpgrades)
                            ? maxConcurrentAgentUpgrades
                            : 0,
                    UpgradeGracePeriodMinutes =
                        int.TryParse(configurationDtos.SingleOrDefault(cv => cv.Name == "UpgradeGracePeriodMinutes")?.Value, out var upgradeGracePeriodMinutes)
                            ? upgradeGracePeriodMinutes
                            : 0
                };
            });
        }

        public async Task<AgentTerminalConfigurationDTO> GetAgentTerminalConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(AgentTerminalConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(AgentTerminalConfigurationDTO.Category);

                return new AgentTerminalConfigurationDTO
                {
                    BaseUrlAgentProxy = configurationDtos.SingleOrDefault(cv => cv.Name == "BaseUrlAgentProxy")?.Value,
                    PreSharedKey = configurationDtos.SingleOrDefault(cv => cv.Name == "PreSharedKey")?.Value,
                    MinAgentVersionToSendEveryKeyInRemoteCommands = configurationDtos.SingleOrDefault(cv => cv.Name == "MinAgentVersionToSendEveryKeyInRemoteCommands")?.Value
                };
            });
        }

        public async Task<DDoSMitigationConfigurationDTO> GetDDoSMitigationConfiguration()
        {
            return await _cache.GetOrCreate(GetCacheKeyByCategoryAndOrganizationId(DDoSMitigationConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(DDoSMitigationConfigurationDTO.Category);

                return new DDoSMitigationConfigurationDTO
                {
                    BaseURL = configurationDtos.SingleOrDefault(cv => cv.Name.Equals("BaseURL", StringComparison.CurrentCultureIgnoreCase))
                        ?.Value,
                    Username = configurationDtos.SingleOrDefault(cv => cv.Name.Equals("Username", StringComparison.CurrentCultureIgnoreCase))
                        ?.Value,
                    Password = configurationDtos
                        .SingleOrDefault(cv => cv.Name.Equals("Password", StringComparison.CurrentCultureIgnoreCase)).Value
                };
            });
        }

        public async Task<SignalRClientConfigurationDTO> GetSignalRClientConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(SignalRClientConfigurationDTO.Category), async entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromDays(1);
                var configurationDtos = await GetByCategory(SignalRClientConfigurationDTO.Category);

                return new SignalRClientConfigurationDTO
                {
                    BaseUrl = configurationDtos.SingleOrDefault(cv => cv.Name == "BaseUrl")?.Value,
                    ApiKey = configurationDtos.SingleOrDefault(cv => cv.Name == "ApiKey")?.Value,
                    RemoteCommandsUrlSegment = configurationDtos.SingleOrDefault(cv => cv.Name == "RemoteCommandsUrlSegment")?.Value,
                    UploadInstallerUrlSegment = configurationDtos.SingleOrDefault(cv => cv.Name == "UploadInstallerUrlSegment")?.Value,
                    DefaultInstallerFilePath = configurationDtos.SingleOrDefault(cv => cv.Name == "DefaultInstallerFilePath")?.Value,
                    AgentUpgradeUrlSegment = configurationDtos.SingleOrDefault(cv => cv.Name == "AgentUpgradeUrlSegment")?.Value,
                };
            });
        }

        public async Task<AgentUpgradeProcessorWorkerConfigurationDTO> GetAgentUpgradeProcessorWorkerConfiguration()
        {
            return await _cache.GetOrCreateAsync(GetCacheKeyByCategoryAndOrganizationId(AgentUpgradeProcessorWorkerConfigurationDTO.Category), async entry =>
            {
                entry.AbsoluteExpiration = GetDefaultCacheExpirationTSForWorkers();
                var configurationDtos = await GetByCategory(AgentUpgradeProcessorWorkerConfigurationDTO.Category);

                return new AgentUpgradeProcessorWorkerConfigurationDTO
                {
                    WorkerDelay = configurationDtos.Count != 0 ? int.Parse(configurationDtos.First(v => v.Name.EndsWith(WorkerConfigurationBase.WorkerDelayKey)).Value) : 0
                };
            });
        }

        public async Task<MsrcConfigurationDTO> GetMsrcConfiguration(Action onEvicted = null)
        {
            var cacheKey = GetCacheKeyByCategoryAndOrganizationId(MsrcConfigurationDTO.Category);
            if (!_cache.TryGetValue(cacheKey, out MsrcConfigurationDTO msrcConfiguration))
            {
                var configurationDtos = await GetByCategory(MsrcConfigurationDTO.Category);

                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30)
                };

                options.RegisterPostEvictionCallback((evictedKey, evictedValue, reason, _) =>
                {
                    if (_evictionHandlers.TryGetValue(evictedKey?.ToString(), out var handlers))
                    {
                        foreach (var h in handlers)
                            h.Invoke();
                    }
                });

                msrcConfiguration = new MsrcConfigurationDTO
                {
                    BaseURL = configurationDtos.SingleOrDefault(cv => cv.Name.Equals("BaseURL", StringComparison.CurrentCultureIgnoreCase))
                        ?.Value,
                    ScheduledTime = int.TryParse(configurationDtos.FirstOrDefault(cv => cv.Name == "ScheduledTime")?.Value, out int scheduledTime)
                        ? scheduledTime
                        : 0
                };
                _cache.Set(cacheKey, msrcConfiguration, options);
            }

            if (onEvicted != null)
            {
                SubscribeToEviction(cacheKey, onEvicted);
            }

            return msrcConfiguration;
        }

        private async Task<List<ConfigurationValueDTO>> GetByCategory(string category, int organizationId = Constants.RootOrganizationId)
        {
            var result = await _configurationRepository.GetConfigurationByCategoryAndOrganization(category, organizationId);
            return result.Select(value => new ConfigurationValueDTO
            {
                Id = value.Id,
                InputType = value.InputType,
                IsSecret = value.IsSecret,
                Name = value.Name,
                Value = value.Value
            }).ToList();
        }

        private static string GetCacheKeyByCategoryAndOrganizationId(string category, int organizationId = Constants.RootOrganizationId)
        {
            return $"{category}_{organizationId}";
        }

        /// <summary>
        /// Temporary workaround: We need a shared cache between the Client and Worker projects. Currently, cache entry keys are removed when modified through the UI. However, without a distributed cache, the Worker project cannot automatically reflect these changes. As a result, we manually set expiration for the keys.
        /// </summary>
        private static DateTimeOffset GetDefaultCacheExpirationTSForWorkers()
        {
            return DateTimeOffset.UtcNow.AddMinutes(5);
        }

        private void ExpireCache(int organizationId)
        {
            // Expire cache entries that are not specific to a DTO category
            foreach (var prop in typeof(CacheEntryKeys).GetProperties())
            {
                _cache.Remove(prop);
            }

            var organizations = _organizationRepository.GetDescendantOrganizations(organizationId).Select(o => o.OrganizationId);

            foreach (var organization in organizations)
            {
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(AdaptiveCloudApiConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(AgentAuthenticationConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(AgentConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(AgentCleanupProcessorWorkerConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(AlertRuleEvaluatorWorkerConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(ApproveUpdatesProcessorWorkerConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(AutoApproveUpdatesProcessorWorkerConfiguration.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(CloudInfrastructureConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(CloudInfrastructureReconcileConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(CloudInfrastructureUsageLimitConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(CookiesConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(ConnectWiseConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(DataProtectionConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(DeclineUpdatesProcessorWorkerConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(EmailSenderWorkerConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(FeatureFlagConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(FileAdministrationConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(HomeConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(ImapConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(KeyCloakConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(KeyCloakServiceConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(MetricsProcessorWorkerConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(NetworkFileServersConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(PartnerResourceHubConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(PowerDNSConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(OnTapConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(SoftwareLicenseAgreementsConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(TotalTechConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(TenaxConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(WhiteLabelConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(ReportsProcessorConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(AgentTerminalConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(SignalRClientConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(DDoSMitigationConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(MsrcConfigurationDTO.Category, organization));
                _cache.Remove(GetCacheKeyByCategoryAndOrganizationId(AgentUpgradeProcessorWorkerConfigurationDTO.Category, organization));
            }
        }

        private void SubscribeToEviction(string key, Action handler)
        {
            if (!_evictionHandlers.TryGetValue(key, out var handlers))
            {
                handlers = new List<Action>();
                _evictionHandlers[key] = handlers;
            }

            handlers.Add(handler);
        }
    }
}