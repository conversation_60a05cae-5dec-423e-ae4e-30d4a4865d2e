-- Manual fix for Data Protection configuration
-- Insert Data Protection configuration category if it doesn't exist
INSERT IGNORE INTO Configuration (Category, IsActive) VALUES ('Data Protection', 1);

-- Get the configuration ID
SET @configId = (SELECT ConfigurationId FROM Configuration WHERE Category = 'Data Protection');

-- Insert configuration values if they don't exist
INSERT IGNORE INTO ConfigurationValues (Name, Value, IsSecret, ConfigurationId, UpdatedDate, UpdatedBy, InputType) 
VALUES 
('BaseURL', '', 0, @configId, NOW(), 1, 'input'),
('ClientId', '', 0, @configId, NOW(), 1, 'input'),
('ClientSecret', '', 1, @configId, NOW(), 1, 'input');

-- Verify the entries
SELECT c.Category, cv.Name, cv.Value, cv.IsSecret 
FROM Configuration c 
JOIN ConfigurationValues cv ON c.ConfigurationId = cv.ConfigurationId 
WHERE c.Category = 'Data Protection';
